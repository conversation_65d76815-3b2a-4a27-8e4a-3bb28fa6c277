""""errors"""

import json
from enum import IntEnum

from fastapi import Response


class ErrorCode(IntEnum):
    SERVER_OK = 200 # Success.

    SERVER_PARAM_ERR = 400 # Request is not valid.
    SERVER_TASK_NOT_EXIST = 404 # Task is not exist.

    SERVER_INTERNAL_ERR = 500 # Internal error.
    SERVER_NETWORK_ERR = 502 # Network exception.
    SERVER_UNKNOWN_ERR = 509 # Unknown error occurred.
    SERVER_FULL_QUEUE_ERR = 510
    



ErrorMsg = {
    ErrorCode.SERVER_OK: "Success.",
    ErrorCode.SERVER_PARAM_ERR: "Request is not valid.",
    ErrorCode.SERVER_TASK_NOT_EXIST: "Task is not exist.",
    ErrorCode.SERVER_INTERNAL_ERR: "Internal error.",
    ErrorCode.SERVER_NETWORK_ERR: "Network exception.",
    ErrorCode.SERVER_UNKNOWN_ERR: "Unknown error occurred.",
    ErrorCode.SERVER_FULL_QUEUE_ERR: "server queue is full."
}


def failed_response(code=ErrorCode.SERVER_INTERNAL_ERR, message="", data=None):
    """Interface call failure response

    Args:
        code (int): error code number
        message (str, optional): Interface call failure information. Defaults to "".
        data (dict, optional): Additional error data. Defaults to None.

    Returns:
        dict: failure json information.
    """
    if not message:
        message = ErrorMsg.get(code, "Unknown error occurred.")

    res = {"code": int(code), "message": message}
    if data is not None:
        res["data"] = data

    return res


def success_response(code=0, message="Success", data=None):
    """Interface call success response

    Args:
        message (str, optional): Success message. Defaults to "Success".
        data (dict, optional): Response data. Defaults to None.

    Returns:
        dict: success json information.
    """
    res = {"code": int(code), "message": message}
    if data is not None:
        res["data"] = data

    return res

